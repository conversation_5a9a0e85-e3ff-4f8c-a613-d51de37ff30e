.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 30px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

@media (max-width: 480px) {
  .app {
    padding: 15px;
  }

  .app h1 {
    font-size: 2rem;
    margin-bottom: 20px;
  }
}
