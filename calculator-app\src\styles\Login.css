/* Login Container */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Login Card */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Login Header */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.login-header p {
  color: #7f8c8d;
  font-size: 1rem;
  margin: 0;
  font-weight: 400;
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0;
}

/* Input Wrapper for Mobile Number */
.input-wrapper {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.country-code {
  background: #e9ecef;
  color: #495057;
  padding: 16px 12px;
  font-weight: 500;
  font-size: 0.95rem;
  border-right: 1px solid #dee2e6;
}

/* Form Inputs */
.form-input {
  width: 100%;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  background: #f8f9fa;
  color: #2c3e50;
  transition: all 0.3s ease;
  outline: none;
}

.input-wrapper .form-input {
  border: none;
  background: transparent;
  flex: 1;
}

.form-input:focus {
  border-color: #3498db;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.input-wrapper.error {
  border-color: #e74c3c;
}

.form-input::placeholder {
  color: #95a5a6;
  font-size: 0.95rem;
}

/* Error Messages */
.error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  font-weight: 500;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.error-message::before {
  content: "⚠️";
  font-size: 0.8rem;
}

.general-error {
  background: #fdf2f2;
  border: 1px solid #fadbd8;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
}

/* Login Button */
.login-button {
  background: linear-gradient(145deg, #3498db, #2980b9);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(145deg, #5dade2, #3498db);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(52, 152, 219, 0.4);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-button.loading {
  background: linear-gradient(145deg, #95a5a6, #7f8c8d);
}

/* Spinner Animation */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Login Footer */
.login-footer {
  margin-top: 30px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.login-footer p {
  color: #7f8c8d;
  font-size: 0.85rem;
  margin: 0;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: 15px;
  }
  
  .login-card {
    padding: 30px 25px;
  }
  
  .login-header h1 {
    font-size: 1.75rem;
  }
  
  .form-input {
    padding: 14px;
    font-size: 0.95rem;
  }
  
  .country-code {
    padding: 14px 10px;
    font-size: 0.9rem;
  }
  
  .login-button {
    padding: 14px 20px;
    font-size: 0.95rem;
  }
}

@media (max-width: 320px) {
  .login-card {
    padding: 25px 20px;
  }
  
  .login-header h1 {
    font-size: 1.5rem;
  }
  
  .form-input {
    padding: 12px;
  }
  
  .country-code {
    padding: 12px 8px;
  }
}
