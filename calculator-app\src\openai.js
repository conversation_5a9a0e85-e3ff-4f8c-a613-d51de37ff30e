// openai.js
// WARNING: Never expose your OpenAI API key in frontend code in production!
// For demo/local development only. In production, use a backend proxy.

import { OpenAI } from "openai";

const apiKey = import.meta.env.VITE_OPENAI_API_KEY;

let openai = null;

if (apiKey && apiKey.startsWith("sk-")) {
  openai = new OpenAI({
    apiKey,
    dangerouslyAllowBrowser: true, // Required for browser usage, see OpenAI docs
  });
} else {
  // Warn in console, but don't crash the app
  console.warn(
    "OpenAI API key is missing or invalid. Please set VITE_OPENAI_API_KEY in your .env file."
  );
}

export default openai;
